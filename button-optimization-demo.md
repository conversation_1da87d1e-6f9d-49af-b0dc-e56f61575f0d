# 工序步骤按钮优化方案

## 优化前的问题

### 当前按钮显示情况：
1. **开始按钮** - 未开始状态显示
2. **领料按钮** - 特定工序步骤显示（3种不同的领料工序）
3. **完成按钮** - 执行中状态显示  
4. **入库按钮** - 特定工序步骤显示（3种不同的入库工序）
5. **提交缺陷按钮** - 执行中和已完成状态显示

### 主要问题：
- 在某些工序状态下，可能同时显示3-4个按钮
- 按钮水平排列，在小屏幕上容易拥挤
- 按钮优先级不明确，用户难以快速找到主要操作

## 优化方案：主要操作 + 更多操作菜单

### 设计原则：
1. **优先级明确**：最重要的1-2个操作直接显示
2. **界面简洁**：减少视觉干扰，提升用户体验
3. **功能完整**：所有原有功能都保留，通过"更多"菜单访问
4. **移动端友好**：适配小屏幕，避免按钮拥挤

### 按钮优先级设计：

#### 主要操作（直接显示）：
1. **开始按钮** - 最高优先级，工序流程的起点
2. **领料按钮** - 高优先级，统一处理所有领料操作
3. **完成按钮** - 最高优先级，工序流程的终点
4. **入库按钮** - 高优先级，统一处理所有入库操作

#### 次要操作（更多菜单）：
1. **提交缺陷按钮** - 相对低频的操作，放入更多菜单

### 技术实现：

#### 1. 统一处理方法
```javascript
// 统一领料处理
handleMaterialPickingUnified(order, product, step) {
  if (step.stepName === '领取PCB裸板') {
    this.handleMaterialPicking(order, product, step)
  } else if (step.stepName === '申领半成品PCB板及零件') {
    this.handleSemifinshedMaterialPicking(order, product, step)
  } else if (step.stepName === '测试员领取焊接半成品') {
    this.handleSemifinshedProductThree(order, product, step)
  }
}

// 统一入库处理
handleStorageUnified(order, product, step) {
  if (step.stepName === '分类清点数量入库') {
    this.handleStorageScan(order, product, step)
  } else if (step.stepName === '清点上下板数量入线边仓') {
    this.handleStorageScanTwo(order, product, step)
  } else if (step.stepName === '产品入库') {
    this.handleStorageScanThree(order, product, step)
  }
}
```

#### 2. 更多操作菜单
```javascript
showMoreActions(order, product, step) {
  const actions = []
  
  // 动态添加可用的次要操作
  if ((step.isCompleted === 1 || step.isCompleted === 2) && 
      order.orderStatus !== 'COMPLETED' && 
      order.orderStatus !== 'PAUSED') {
    actions.push('提交缺陷')
  }
  
  // 显示操作选择菜单
  uni.showActionSheet({
    itemList: actions,
    success: (res) => {
      const selectedAction = actions[res.tapIndex]
      // 执行对应操作
    }
  })
}
```

#### 3. 响应式布局
```scss
.step-actions-optimized {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8rpx;

  .primary-actions {
    display: flex;
    align-items: center;
    gap: 8rpx;
    flex: 1;
  }

  .more-actions {
    display: flex;
    align-items: center;
  }
}
```

### 用户体验改进：

#### 优化前：
```
[开始] [领料] [完成] [提交缺陷]  // 4个按钮，界面拥挤
```

#### 优化后：
```
[开始] [领料]     [更多 ⋯]  // 2个主要按钮 + 1个更多按钮，界面简洁
```

### 移动端适配：
- 小屏幕下按钮尺寸自动调整
- 间距优化，避免误触
- 更多菜单使用系统原生样式，用户熟悉度高

## 预期效果

1. **界面更简洁**：减少50%的直接显示按钮数量
2. **操作更清晰**：主要操作一目了然
3. **体验更流畅**：减少界面拥挤，提升点击准确性
4. **功能完整性**：所有原有功能都保留
5. **扩展性更好**：未来新增操作可以灵活加入更多菜单

## 实施建议

1. **渐进式部署**：先在测试环境验证用户反馈
2. **用户培训**：简单说明"更多"按钮的使用方式
3. **数据监控**：跟踪按钮点击率，优化优先级设置
4. **持续优化**：根据用户使用习惯调整按钮优先级
